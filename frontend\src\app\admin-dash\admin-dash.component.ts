import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';
import { ResponsableService } from '../responsable.service';
import { User } from '../model/user.model';
import * as feather from 'feather-icons';
import { Chart, registerables } from 'chart.js';
import { Router } from '@angular/router';
import { AuthenticationService } from '../auth/authentication.service';
import { Transaction } from '../model/transaction.model';
import { TransactionService } from '../services/transaction.service';
import { Modal } from 'bootstrap';

@Component({
  selector: 'app-respon-dashboard',
  templateUrl: './admin-dash.component.html',
  styleUrls: ['./admin-dash.component.css']
})
export class AdminDashComponent implements AfterViewInit, OnInit {
  @ViewChild('myChart') myChartRef!: ElementRef;
  @ViewChild('myBarChart') myBarChartRef!: ElementRef;
  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;
  responsables: User[] = [];
  adminName: string = '';
  private chartInstance: any;
  private barChartInstance: any;
  transactions: Transaction[] = [];
  modalMessage = '';
  private modalInstance!: Modal;

  constructor(
    private responsableService: ResponsableService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    public authService: AuthenticationService,
    private transactionService: TransactionService,


  ) {
    // Registering chart.js components
    Chart.register(...registerables);
  }



  // Function to load transactions from the server
  loadTransactions(): void {
    this.transactionService.getAllTransactions().subscribe({
      next: (data) => {
        this.transactions = data;
        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded

        // Now render both charts
        this.renderChart();
        this.renderBarChart();
      },
      error: (err) => {
        console.error('Error fetching transactions', err);
      }
    });
  }


  // Function to render the line chart using the transaction data
  renderChart(): void {
    const ctx = this.myChartRef.nativeElement.getContext('2d');

    // Destroy previous chart if exists
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }

    const labels = this.transactions.map(t => t.type);
    const data = this.transactions.map(t => t.montatnt);

    // Create a new line chart instance
    this.chartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Montant par Type de Transaction',
          data: data,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 2,
          fill: false
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Function to render the bar chart using the transaction data
  renderBarChart(): void {
    const ctx = this.myBarChartRef.nativeElement.getContext('2d');

    // Destroy previous chart if exists
    if (this.barChartInstance) {
      this.barChartInstance.destroy();
    }

    // Group transactions by type and sum amounts
    const typeAmounts = this.getTypeAmounts();
    const labels = Object.keys(typeAmounts);
    const data = Object.values(typeAmounts);

    // Create a new bar chart instance
    this.barChartInstance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Montant Total par Type',
          data: data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 205, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Helper methods for calculations
  getTotalAmount(): number {
    return this.transactions.reduce((total, transaction) => total + (transaction.montatnt || 0), 0);
  }

  getAverageAmount(): number {
    if (this.transactions.length === 0) return 0;
    return this.getTotalAmount() / this.transactions.length;
  }

  getUniqueTypes(): string[] {
    const types = this.transactions.map(t => t.type).filter(type => type);
    return [...new Set(types)];
  }

  getTypeAmounts(): { [key: string]: number } {
    const typeAmounts: { [key: string]: number } = {};
    this.transactions.forEach(transaction => {
      if (transaction.type && transaction.montatnt) {
        if (typeAmounts[transaction.type]) {
          typeAmounts[transaction.type] += transaction.montatnt;
        } else {
          typeAmounts[transaction.type] = transaction.montatnt;
        }
      }
    });
    return typeAmounts;
  }


  ngOnInit(): void {
    const adminData = localStorage.getItem('admin');
    if (adminData) {
      const admin = JSON.parse(adminData);
      this.adminName = `${admin.prenom} ${admin.nom}`;
    } else {
      this.adminName = 'Admin';
    }

    this.loadResponsables();
  }

  loadResponsables() {
    this.responsableService.getResponsables().subscribe(
      (data) => {
        this.responsables = data;
      },
      (error) => {
        console.error('Error fetching responsables:', error);
      }
    );
  }

  deleteResponsable(id: number) {
    if (confirm('Are you sure you want to delete this Responsable?')) {
      this.responsableService.deleteResponsable(id).subscribe({
        next: (response) => {
          console.log('Delete response:', response);
          this.responsables = this.responsables.filter(responsable => responsable.id !== id);
        },
        error: (error) => {
          console.error('Error deleting responsable:', error);
        }
      });
    }
  }

  logout() {
    localStorage.removeItem('jwt_token');
    localStorage.removeItem('role');
    localStorage.removeItem('group');
    localStorage.removeItem('admin'); // ✅ correct key now
    console.log(localStorage.getItem('jwt_token')); // should be null after logout
    this.router.navigate(['/login']);
  }

  updateResponsable(id: number, updatedResponsable: User) {
    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(
      () => {
        this.loadResponsables();
      },
      (error) => {
        console.error('Error updating responsable:', error);
      }
    );
  }

  trackById(index: number, item: User): number | undefined {
    return item.id ?? 0;
  }

  ngAfterViewInit() {
    this.loadTransactions();
    feather.replace();
    if (this.feedbackModalRef) {
      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);
    }
  }

}
