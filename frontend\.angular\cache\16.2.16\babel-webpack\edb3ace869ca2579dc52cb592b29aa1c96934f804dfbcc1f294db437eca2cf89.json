{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component'; // Import AddResponsableComponent\nimport { HabilitationComponent } from './habilitation/habilitation.component'; // Import the component\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: 'edit-responsable/:id',\n  component: ResponsableEditComponent\n}, {\n  path: 'adminDash',\n  component: AdminDashComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'transactions',\n  component: TransactionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'action',\n  component: ActionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'actionnaires',\n  component: ActionnairesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'port',\n  component: PortefeuillesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'users',\n  component: UsersComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'groups',\n  component: GroupsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'edit-groupe/:id',\n  component: ModifyGroupComponent\n}, {\n  path: 'add-responsable',\n  component: AddResponsableComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'not-authorized',\n  component: NotAuthorizedComponent\n}, {\n  path: 'habilitation/:id',\n  component: HabilitationComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "SignupComponent", "NotAuthorizedComponent", "<PERSON><PERSON><PERSON><PERSON>", "TransactionsComponent", "ActionsComponent", "ActionnairesComponent", "PortefeuillesComponent", "AdminDashComponent", "ResponsableEditComponent", "UsersComponent", "GroupsComponent", "ModifyGroupComponent", "AddResponsableComponent", "HabilitationComponent", "ForgotPasswordComponent", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component';  // Import AddResponsableComponent\nimport { HabilitationComponent } from './habilitation/habilitation.component';  // Import the component\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\n\nconst routes: Routes = [\n  { path: '', redirectTo: 'login', pathMatch: 'full' },\n  { path: 'login', component: LoginComponent },\n  { path: 'signup', component: SignupComponent },\n  { path: 'edit-responsable/:id', component: ResponsableEditComponent },\n  { path: 'adminDash', component: AdminDashComponent, canActivate: [AuthGuard] },\n  { path: 'transactions', component: TransactionsComponent, canActivate: [AuthGuard] },\n  { path: 'action', component: ActionsComponent, canActivate: [AuthGuard] },\n  { path: 'actionnaires', component: ActionnairesComponent, canActivate: [AuthGuard] },\n  { path: 'port', component: PortefeuillesComponent, canActivate: [AuthGuard] },\n  { path: 'users', component: UsersComponent, canActivate: [AuthGuard] }, // Admin only\n  { path: 'groups', component: GroupsComponent, canActivate: [AuthGuard] }, // Admin only\n  { path: 'edit-groupe/:id', component: ModifyGroupComponent }, // Added route for editing a group\n  { path: 'add-responsable', component: AddResponsableComponent, canActivate: [AuthGuard] },  // Added route for AddResponsableComponent\n  { path: 'not-authorized', component: NotAuthorizedComponent },\n  { path: 'habilitation/:id', component: HabilitationComponent, canActivate: [AuthGuard] },  // Route with the dynamic id\n  { path: 'forgot-password', component: ForgotPasswordComponent },\n\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,uBAAuB,QAAQ,6CAA6C,CAAC,CAAE;AACxF,SAASC,qBAAqB,QAAQ,uCAAuC,CAAC,CAAE;AAChF,SAASC,uBAAuB,QAAQ,6CAA6C;;;AAErF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEpB;AAAc,CAAE,EAC5C;EAAEiB,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAEnB;AAAe,CAAE,EAC9C;EAAEgB,IAAI,EAAE,sBAAsB;EAAEG,SAAS,EAAEX;AAAwB,CAAE,EACrE;EAAEQ,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEZ,kBAAkB;EAAEa,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EAC9E;EAAEc,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEhB,qBAAqB;EAAEiB,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACpF;EAAEc,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAEf,gBAAgB;EAAEgB,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACzE;EAAEc,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEd,qBAAqB;EAAEe,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACpF;EAAEc,IAAI,EAAE,MAAM;EAAEG,SAAS,EAAEb,sBAAsB;EAAEc,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EAC7E;EAAEc,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEV,cAAc;EAAEW,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACtE;EAAEc,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAET,eAAe;EAAEU,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACxE;EAAEc,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAER;AAAoB,CAAE,EAC5D;EAAEK,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEP,uBAAuB;EAAEQ,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACzF;EAAEc,IAAI,EAAE,gBAAgB;EAAEG,SAAS,EAAElB;AAAsB,CAAE,EAC7D;EAAEe,IAAI,EAAE,kBAAkB;EAAEG,SAAS,EAAEN,qBAAqB;EAAEO,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACxF;EAAEc,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEL;AAAuB,CAAE,CAEhE;AAMD,OAAM,MAAOO,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBvB,YAAY,CAACwB,OAAO,CAACP,MAAM,CAAC,EAC5BjB,YAAY;IAAA;EAAA;;;2EAEXuB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAA1B,YAAA;IAAA2B,OAAA,GAFjB3B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}