import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AuthenticationService } from '../auth/authentication.service';
import * as feather from 'feather-icons';
import { HttpClient } from '@angular/common/http';
import { Modal } from 'bootstrap';
import { TransactionService } from '../services/transaction.service';
import { Transaction } from '../model/transaction.model';
import * as XLSX from 'xlsx';
import { Chart, registerables } from 'chart.js';
import * as FileSaver from 'file-saver';

@Component({
  selector: 'app-transactions',
  templateUrl: './transactions.component.html',
  styleUrls: ['./transactions.component.css']
})
export class TransactionsComponent implements AfterViewInit {
  @ViewChild('myChart') myChartRef!: ElementRef;
  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;
  transactions: Transaction[] = [];

  selectedFile!: File;
  modalMessage = '';
  isSuccess = true;
  private modalInstance!: Modal;
  private chartInstance: any;

  constructor(
    private router: Router,
    private http: HttpClient,
    private transactionService: TransactionService,
    public authService: AuthenticationService
  ) {
    // Registering chart.js components
    Chart.register(...registerables);
  }

  // Function to export data to Excel
  exportToExcel(): void {
    const exportData = this.transactions.map(t => ({
      'ID Transaction': t.idTransaction,
      'Type': t.type,
      'Quantité': t.qantite,
      'Montant': t.montatnt,
      'Observations': t.observations,
      'Date de création': t.dateCreation,
      'Date de transaction': t.dateTransaction,
      'ID Utilisateur': t.userId,
      'Nom Portefeuille': t.portefeuille?.idPortefeuille || 'N/A'
    }));

    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData);
    const workbook: XLSX.WorkBook = { Sheets: { 'Transactions': worksheet }, SheetNames: ['Transactions'] };
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    const data: Blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });
    FileSaver.saveAs(data, 'transactions_export.xlsx');
  }

  // Function to load transactions from the server
  loadTransactions(): void {
    this.transactionService.getAllTransactions().subscribe({
      next: (data) => {
        this.transactions = data;
        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded

        // Now render the chart
        this.renderChart();
      },
      error: (err) => {
        console.error('Error fetching transactions', err);
      }
    });
  }

  ngAfterViewInit() {
    this.loadTransactions();
    feather.replace();
    if (this.feedbackModalRef) {
      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);
    }
  }

  // Function to render the chart using the transaction data
  renderChart(): void {
    const ctx = this.myChartRef.nativeElement.getContext('2d');

    // Destroy previous chart if exists
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }

    const labels = this.transactions.map(t => t.type);
    const data = this.transactions.map(t => t.montatnt);

    // Create a new chart instance
    this.chartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Montant par Type de Transaction',
          data: data,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Handle file selection
  onFileSelected(event: any): void {
    this.selectedFile = event.target.files[0];
    if (this.selectedFile) {
      this.uploadExcel();
    }
  }

  // Function to upload the selected Excel file
  uploadExcel(): void {
    if (!this.selectedFile) {
      this.showModal("Veuillez sélectionner un fichier Excel.", false);
      return;
    }

    const formData = new FormData();
    formData.append("file", this.selectedFile);

    this.http.post('http://localhost:8000/api/v1/auth/upload-excel', formData, {
      responseType: 'text'
    }).subscribe({
      next: (response) => {
        this.showModal("Fichier envoyé avec succès : " + response, true);
      },
      error: (error) => {
        this.showModal("Erreur lors de l'envoi du fichier : " + error.error, false);
      }
    });
  }

  // Function to show modal with feedback message
  showModal(message: string, success: boolean): void {
    this.modalMessage = message;
    this.isSuccess = success;
    this.modalInstance.show();
  }

  // File drag-and-drop handlers
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onFileDropped(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer && event.dataTransfer.files.length > 0) {
      this.selectedFile = event.dataTransfer.files[0];
      this.uploadExcel();
    }
  }

  // Logout functionality
  logout() {
    localStorage.removeItem('jwt_token');
    localStorage.removeItem('role');
    localStorage.removeItem('group');
    console.log(localStorage.getItem('jwt_token'));
    this.router.navigate(['/login']);
  }
}
